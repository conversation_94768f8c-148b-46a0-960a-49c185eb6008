// Root-level build.gradle.kts (or build.gradle)
// Plugins and dependencies will be configured for: <PERSON><PERSON><PERSON>, Jetpack Compose, Retrofit, ExoPlayer, Coil, Hilt, Room, WorkManager, optional MQTT

// === settings.gradle.kts ===
rootProject.name = "AdPlayerApp"
include(":app")

// === build.gradle.kts (Project root) ===
buildscript {
    dependencies {
        classpath("com.android.tools.build:gradle:8.3.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0")
        classpath("com.google.dagger:hilt-android-gradle-plugin:2.48")
    }
}

// === app/build.gradle.kts ===
plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-kapt")
    id("dagger.hilt.android.plugin")
}

android {
    namespace = "com.example.adplayer"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.example.adplayer"
        minSdk = 26
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            isMinifyEnabled = false
        }
    }

    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {
    // Jetpack Compose
    implementation("androidx.activity:activity-compose:1.7.2")
    implementation("androidx.compose.ui:ui:1.5.1")
    implementation("androidx.compose.material3:material3:1.2.0")
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1")

    // ExoPlayer
    implementation("androidx.media3:media3-exoplayer:1.2.0")

    // Coil for images
    implementation("io.coil-kt:coil-compose:2.4.0")

    // Retrofit + Moshi
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-moshi:2.9.0")
    implementation("com.squareup.moshi:moshi-kotlin:1.15.0")

    // Room
    implementation("androidx.room:room-runtime:2.6.1")
    kapt("androidx.room:room-compiler:2.6.1")
    implementation("androidx.room:room-ktx:2.6.1")

    // Hilt
    implementation("com.google.dagger:hilt-android:2.48")
    kapt("com.google.dagger:hilt-compiler:2.48")

    // WorkManager
    implementation("androidx.work:work-runtime-ktx:2.9.0")

    // MQTT (optional)
    implementation("org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5")
    implementation("org.eclipse.paho:org.eclipse.paho.android.service:1.1.1")

    // USB Manager
    implementation("androidx.core:core-ktx:1.12.0")
}

// === Hilt Setup (Application class) ===
// MyApp.kt
@HiltAndroidApp
class MyApp : Application()

// === AndroidManifest.xml additions ===
<application
    android:name=".MyApp"
    ... >
    <service
        android:name=".service.ForegroundMonitorService"
        android:foregroundServiceType="connectedDevice|dataSync"
        android:exported="false"/>
</application>
