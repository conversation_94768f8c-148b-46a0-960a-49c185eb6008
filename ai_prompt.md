# AI Prompt: Android Advertising Client with USB-HID and Schedule Playback

**Goal**: Create a lightweight Android app using Kotlin and Jetpack Compose to display advertising content (video, image, HTML) based on a schedule from an API or MQTT, and switch to another app when a USB HID device becomes active.

## Key Requirements

- Kotlin + Jetpack Compose
- Retrofit (API), <PERSON><PERSON> (JSON)
- ExoPlayer (video), Coil (images), WebView (HTML)
- MQTT (optional) via Eclipse Paho
- USB HID detection via UsbManager
- Schedule stored in Room or JSON
- Local caching of media
- ForegroundService for monitoring
- MVVM + Clean Architecture
- Hilt for DI, WorkManager for background sync
- Jetpack DataStore for settings
- Offline support via local cache
- Compatible with Android 8.0+

Refer to `technical_spec.md` for full details.
